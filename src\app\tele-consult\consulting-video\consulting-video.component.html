<!-- Main OpenVidu session -->
<opv-session
  #ovSessionComponent
  [sessionName]="mySessionId"
  [user]="myUserName"
  [tokens]="tokens"
  [ovSettings]="ovSettings"
  (sessionCreated)="handlerSessionCreatedEvent($event)"
  (participantCreated)="handlerPublisherCreatedEvent($event)"
  (error)="handlerErrorEvent($event)"
  (toolbarButtonClicked)="onToolbarButtonClicked($event)"
  *ngIf="session && sessionJoined">
</opv-session>

<!-- Secondary camera container -->
<div #secondaryContainer class="secondary-camera-container" *ngIf="isSecondaryCameraAvailable">
  <!-- Secondary camera stream will be appended here -->
</div>


