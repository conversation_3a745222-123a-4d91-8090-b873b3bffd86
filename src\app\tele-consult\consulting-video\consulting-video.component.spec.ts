import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { ConsultingVideoComponent } from './consulting-video.component';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ToastrModule } from 'ngx-toastr';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

describe('ConsultingVideoComponent', () => {
  let component: ConsultingVideoComponent;
  let fixture: ComponentFixture<ConsultingVideoComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ConsultingVideoComponent],
      imports: [HttpClientTestingModule, ToastrModule.forRoot()],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ConsultingVideoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should handle videoDevices array correctly', () => {
    component.videoDevices = [
      { deviceId: 'primaryCam', kind: 'videoinput' },
      { deviceId: 'secondaryCam', kind: 'videoinput' }
    ];
    expect(component.videoDevices.length).toBeGreaterThanOrEqual(2);
  });

  it('should have showJoinButton output defined', () => {
    expect(component.showJoinButton).toBeDefined();
  });
});
