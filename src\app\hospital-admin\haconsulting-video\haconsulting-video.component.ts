import { Component, Input, OnInit, ViewChild, ɵConsole,Output,EventEmitter } from '@angular/core';
import { throwError as observableThrowError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import {OpenviduSessionComponent, StreamEvent, Session, UserModel, OpenViduLayout, OvSettings, OpenViduLayoutOptions, SessionDisconnectedEvent, Publisher,OpenVidu} from 'openvidu-angular';
import { TeleConsultService } from '../../tele-consult/tele-consult.service';
import { ActivatedRoute } from '@angular/router';
import  * as Settings from '../../config/settings';
import {ToastrService} from 'ngx-toastr';

@Component({
  selector: 'app-haconsulting-video',
  templateUrl: './haconsulting-video.component.html',
  styleUrls: ['./haconsulting-video.component.css']
})
export class HaconsultingVideoComponent implements OnInit {
  OPENVIDU_SERVER_URL = Settings.OPENVIDU_SERVER_URL;
  OPENVIDU_SERVER_SECRET = Settings.OPENVIDU_SECRET;
  mySessionId: string;
  myUserName = 'Participant' + Math.floor(Math.random() * 100);
  tokens: string[] = [];
  deviceTokens: string[] = [];
  session = false;
  ovSession: Session;
  ovLocalUsers: UserModel[];
  ovLayout: OpenViduLayout;
  ovLayoutOptions: OpenViduLayoutOptions;
  // ovRemotesArray: UserModel[];
  ovSettings: OvSettings;

  @Input() consultationId: any;
  @Input() participantName: any;
  @Input() closeVideoSession:any;
  videoToken = '';
  appointmentId = '';
  sessionJoined = false;
  @Output()showJoinButton : EventEmitter<boolean> = new EventEmitter();
  @ViewChild('ovSessionComponent')
  public ovSessionComponent: OpenviduSessionComponent;
  sub: any;
  videoDevices: any=[];
  audioDevices: any=[];

  constructor(
    private httpClient: HttpClient,
    private route: ActivatedRoute,
    private teleConsultService: TeleConsultService,
    private notificationService:ToastrService
    )
    {
      this.mySessionId = this.consultationId;
    }

  ngOnInit(){
    var OV = new OpenVidu();
			OV.getDevices().then(devices => {
			//console.log(devices);
      this.videoDevices = devices.filter(device => device.kind === 'videoinput');
      this.audioDevices = devices.filter(device => device.kind === 'audioinput');
      console.log(this.videoDevices.length,this.audioDevices.length);
      if(this.videoDevices.length>0 && this.audioDevices.length>0){
        this.ovSettings = {
          chat: false,
          autopublish: true,
          toolbarButtons: {
            audio: true,
            video: true,
            screenShare: false,
            fullscreen: true,
            layoutSpeaking: false,
            exit: false,
          }
        };
      }else if(this.audioDevices.length>0){
        this.ovSettings = {
          chat: false,
          autopublish: true,
          toolbarButtons: {
            audio: true,
            video: false,
            screenShare: false,
            fullscreen: true,
            layoutSpeaking: false,
            exit: false,
          }
        };
      }else{
        this.notificationService.error('No video or audio devices have been found. Please, connect at least one. please refresh');

      }

    this.joinSession();
    this.joinDeviceSession();
    this.myUserName = this.participantName;
    console.log('Participant:'+this.participantName)
    });



   }

  async joinSession() {
    console.log('Consulting video cons id::' + this.consultationId);
      this.teleConsultService.getVideoToken(this.consultationId).subscribe((data) => {
        const msg=data['message'];
        if(msg=='Doctor Ended Consultation'){
          this.notificationService.error('Doctor Ended Consultation');
        }else{
          this.videoToken = data['token'];
          this.sessionJoined = true;
          this.tokens.push(this.videoToken);
          this.session = true;
        }

      },
      (err) =>{
        console.log('ERROR:' + err);
        this.notificationService.error('Please refresh page');

      });


  }

  joinDeviceSession(){
    console.log('Consulting device video cons id::' + this.consultationId)
    this.teleConsultService.getDeviceVideoToken(this.consultationId).subscribe((data) => {
      this.videoToken = data['token'];
      this.sessionJoined = true;
      this.deviceTokens.push(this.videoToken);
      this.session = true;
    },
    (err) =>{
      console.log('ERROR:' + err);
    //  this.notificationService.error('Please refresh page');
    });

  }

  handlerSessionCreatedEvent(session: Session): void {
    console.log('SESSION CREATED EVENT', session);
    // localStorage.setItem('sess', this.consultationId);
    session.on('streamCreated', (event: StreamEvent) => {
      console.log('SESSION STREAM CREATED');
    });
    session.on('streamDestroyed', (event: StreamEvent) => {
      console.log('SESSION STREAM DESTROYED');
    });

    session.on('sessionDisconnected', (event: SessionDisconnectedEvent) => {
      this.session = false;
      this.tokens = [];
      this.showJoinButton.emit(false);
    });

    this.myMethod();

  }

  handlerPublisherCreatedEvent(publisher: Publisher) {
    publisher.on('streamCreated', (e) => {
      console.log('Publisher streamCreated', e);
    });

    // Listen for audio/video property changes to prevent conflicts
    publisher.on('streamPropertyChanged', (event) => {
      console.log('HA Publisher stream property changed:', event);

      // Check if this is an audio or video property change
      if (event.changedProperty === 'audioActive' || event.changedProperty === 'videoActive') {
        console.log(`HA ${event.changedProperty} changed to:`, event.newValue);
        // Add any hospital admin specific handling here if needed
      }
    });
  }

  handlerErrorEvent(event): void {
    console.log(event);
  }

  myMethod() {
    this.ovLocalUsers = this.ovSessionComponent.getLocalUsers();
    this.ovLayout = this.ovSessionComponent.getOpenviduLayout();
    this.ovLayoutOptions = this.ovSessionComponent.getOpenviduLayoutOptions();
  }

  /**
   * --------------------------
   * SERVER-SIDE RESPONSIBILITY
   * --------------------------
   * This method retrieve the mandatory user token from OpenVidu Server,
   * in this case making use Angular http API.
   * This behavior MUST BE IN YOUR SERVER-SIDE IN PRODUCTION. In this case:
   *   1) Initialize a session in OpenVidu Server	 (POST /api/sessions)
   *   2) Generate a token in OpenVidu Server		   (POST /api/tokens)
   *   3) The token must be consumed in Session.connect() method of OpenVidu Browser
   */

  getToken(): Promise<string> {
    return this.createSession(this.consultationId).then((sessionId) => {
      return this.createToken(sessionId);
    });
  }

  createSession(sessionId) {
    return new Promise((resolve, reject) => {
      const body = JSON.stringify({ customSessionId: sessionId });
      const options = {
        headers: new HttpHeaders({
          Authorization: 'Basic ' + btoa('OPENVIDUAPP:' + this.OPENVIDU_SERVER_SECRET),
          'Content-Type': 'application/json',
        }),
      };
      return this.httpClient
        .post(this.OPENVIDU_SERVER_URL + '/api/sessions', body, options)
        .pipe(
          catchError((error) => {
            if (error.status === 409) {
              resolve(sessionId);
            } else {
              console.log('ERROR STATUS CODE::'+error.status);
              console.log('ERRROR DETAIL::'+error);
              console.warn('No connection to OpenVidu Server. This may be a certificate error at ' + this.OPENVIDU_SERVER_URL);
              if (
                window.confirm(
                  'No connection to OpenVidu Server. This may be a certificate error at "' +
                    this.OPENVIDU_SERVER_URL +
                    '"\n\nClick OK to navigate and accept it. If no certificate warning is shown, then check that your OpenVidu Server' +
                    'is up and running at "' +
                    this.OPENVIDU_SERVER_URL +
                    '"',
                )
              ) {
                // location.assign(this.OPENVIDU_SERVER_URL + '/accept-certificate');
              }
            }
            return observableThrowError(error);
          }),
        )
        .subscribe((response) => {
          console.log('session openvidu',response);
          resolve(response['id']);
        });
    });
  }

  createToken(sessionId): Promise<string> {
    return new Promise((resolve, reject) => {
      const body = JSON.stringify({ session: sessionId });
      const options = {
        headers: new HttpHeaders({
          Authorization: 'Basic ' + btoa('OPENVIDUAPP:' + this.OPENVIDU_SERVER_SECRET),
          'Content-Type': 'application/json',
        }),
      };
      return this.httpClient
        .post(this.OPENVIDU_SERVER_URL + '/api/tokens', body, options)
        .pipe(
          catchError((error) => {
            reject(error);
            return observableThrowError(error);
          }),
        )
        .subscribe((response) => {
          console.log(response);
          resolve(response['token']);
        });
    });
  }

}
